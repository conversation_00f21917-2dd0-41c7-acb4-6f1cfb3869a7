<!DOCTYPE html>
<html lang="nl" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Tita's Baby Shop</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body class="d-flex align-items-center min-vh-100">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <!-- Logo/Brand -->
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="bi bi-shop display-1 text-primary"></i>
                    </div>
                    <h2 class="fw-bold text-white">Tita's Baby Shop</h2>
                    <p class="text-muted">Admin Panel</p>
                </div>

                <!-- Login Card -->
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-shield-lock"></i> Admin Login
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <!-- Login Form -->
                        <form method="POST" id="loginForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Username Field -->
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    {{ form.username(class="form-control") }}
                                </div>
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Password Field -->
                            <div class="mb-4">
                                {{ form.password.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    {{ form.password(class="form-control") }}
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center text-muted">
                        <small>
                            <i class="bi bi-info-circle"></i>
                            Alleen geautoriseerde gebruikers hebben toegang
                        </small>
                    </div>
                </div>

               
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="position-fixed bottom-0 w-100 bg-dark text-light py-2">
        <div class="container text-center">
            <small>&copy; 2025 Tita's Baby Shop - Beveiligd Admin Panel</small>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password toggle functionality
            const togglePassword = document.getElementById('togglePassword');
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (togglePassword && passwordField && toggleIcon) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);
                    
                    // Toggle icon
                    if (type === 'password') {
                        toggleIcon.className = 'bi bi-eye';
                    } else {
                        toggleIcon.className = 'bi bi-eye-slash';
                    }
                });
            }

            // Auto-dismiss alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // Focus on username field
            const usernameField = document.getElementById('username');
            if (usernameField) {
                usernameField.focus();
            }

            // Add loading state to form submission
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function() {
                    const submitBtn = loginForm.querySelector('input[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        const originalText = submitBtn.value;
                        submitBtn.value = 'Bezig met inloggen...';
                        
                        // Re-enable after 10 seconds as fallback
                        setTimeout(function() {
                            submitBtn.disabled = false;
                            submitBtn.value = originalText;
                        }, 10000);
                    }
                });
            }
        });
    </script>

    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }
        
        .card {
            border: none;
            backdrop-filter: blur(10px);
        }
        
        .card-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .card-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(45, 45, 45, 0.5);
        }
        
        .input-group-text {
            background-color: #3d3d3d;
            border-color: #555;
            color: #e9ecef;
        }
        
        .btn-outline-secondary {
            border-color: #555;
            color: #6c757d;
        }
        
        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: #fff;
        }
        
        footer {
            background-color: rgba(26, 26, 26, 0.9) !important;
            backdrop-filter: blur(10px);
        }
        
        .border-warning {
            border-color: #ffc107 !important;
        }
        
        code {
            background-color: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
        }
    </style>
</body>
</html>
